#!/usr/bin/env node

// 测试脚本：验证codebaseLogging模块的调试日志
// 使用方法：node test-codebase-logging.js

const { CodebaseLogger } = require('./core/indexing/CodebaseLogger.ts');

console.log('=== 开始测试codebaseLogging模块 ===');

// 测试1: 创建CodebaseLogger实例
console.log('\n--- 测试1: 创建CodebaseLogger实例 ---');
const logger = CodebaseLogger.getInstance({
  enabled: true,
  logLevel: 'debug',
  maxLogFileSize: 1024 * 1024,
  logRetentionDays: 7,
  bufferSize: 5
});

// 测试2: 更新配置
console.log('\n--- 测试2: 更新配置 ---');
logger.updateConfig({
  enabled: true,
  logLevel: 'info',
  bufferSize: 10
});

// 测试3: 记录索引开始日志
console.log('\n--- 测试3: 记录索引开始日志 ---');
logger.logIndexingStart({
  totalFiles: 100,
  indexType: 'chunks',
  branch: 'main'
}, 'info', '/test/workspace');

// 测试4: 记录文件处理日志
console.log('\n--- 测试4: 记录文件处理日志 ---');
logger.logFileProcessing({
  filepath: '/test/file.ts',
  fileSize: 1024,
  language: 'typescript',
  processingTimeMs: 50,
  chunksGenerated: 3
}, 'debug', '/test/workspace');

// 测试5: 记录检索开始日志
console.log('\n--- 测试5: 记录检索开始日志 ---');
logger.logRetrievalStart({
  originalQuery: 'test query',
  fullInput: 'test query with context',
  nRetrieve: 20,
  nFinal: 10,
  useReranking: true,
  filterDirectory: '/test'
}, 'info', '/test/workspace');

// 测试6: 手动刷新缓冲区
console.log('\n--- 测试6: 手动刷新缓冲区 ---');
setTimeout(async () => {
  await logger.flush();
  console.log('\n=== 测试完成 ===');
}, 1000);
